package ir.rahavardit.ariel.data.repository

import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.FAQ
import ir.rahavardit.ariel.data.model.PaginatedResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Repository class that handles FAQ-related operations.
 */
class FAQRepository {

    private val apiService = RetrofitClient.getApiService()

    /**
     * Retrieves the list of FAQ items with pagination.
     *
     * @param token The authentication token.
     * @param pageSize The number of FAQ items per page.
     * @return A Result containing either the paginated response or an Exception.
     */
    suspend fun getFAQs(token: String, pageSize: Int = 15): Result<PaginatedResponse<FAQ>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getFAQs(authToken, pageSize)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch FAQs: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves FAQ items from a specific URL (for pagination).
     *
     * @param token The authentication token.
     * @param url The full URL for the next page.
     * @return A Result containing either the paginated response or an Exception.
     */
    suspend fun getFAQsFromUrl(token: String, url: String): Result<PaginatedResponse<FAQ>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getFAQsFromUrl(authToken, url)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch FAQs: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the details of a specific FAQ item by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the FAQ item.
     * @return A Result containing either the FAQ item details or an Exception.
     */
    suspend fun getFAQDetails(token: String, shortUuid: String): Result<FAQ> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getFAQDetails(authToken, shortUuid)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch FAQ details: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Creates a new FAQ item.
     *
     * @param token The authentication token.
     * @param title The title of the FAQ item.
     * @param body The content body of the FAQ item.
     * @return A Result containing either the created FAQ item or an Exception.
     */
    suspend fun createFAQ(token: String, title: String, body: String): Result<FAQ> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val faqRequest = mapOf(
                    "title" to title,
                    "body" to body
                )
                val response = apiService.createFAQ(authToken, faqRequest)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to create FAQ: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Deletes a FAQ item by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the FAQ item to delete.
     * @return A Result indicating success or failure.
     */
    suspend fun deleteFAQ(token: String, shortUuid: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.deleteFAQ(authToken, shortUuid)

                if (response.isSuccessful) {
                    Result.success(Unit)
                } else {
                    Result.failure(Exception("Failed to delete FAQ: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Updates a FAQ item by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the FAQ item to update.
     * @param title The updated title of the FAQ item.
     * @param body The updated content body of the FAQ item.
     * @return A Result containing either the updated FAQ item or an Exception.
     */
    suspend fun updateFAQ(token: String, shortUuid: String, title: String, body: String): Result<FAQ> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val faqRequest = mapOf(
                    "title" to title,
                    "body" to body
                )
                val response = apiService.updateFAQ(authToken, shortUuid, faqRequest)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to update FAQ: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
}
