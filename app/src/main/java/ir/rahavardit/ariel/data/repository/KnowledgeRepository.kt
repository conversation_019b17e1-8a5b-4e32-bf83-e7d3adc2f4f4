package ir.rahavardit.ariel.data.repository

import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.Knowledge
import ir.rahavardit.ariel.data.model.PaginatedResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Repository class that handles knowledge-related operations.
 */
class KnowledgeRepository {

    private val apiService = RetrofitClient.getApiService()

    /**
     * Retrieves the list of knowledge items with pagination.
     *
     * @param token The authentication token.
     * @param pageSize The number of knowledge items per page.
     * @return A Result containing either the paginated response or an Exception.
     */
    suspend fun getKnowledges(token: String, pageSize: Int = 15): Result<PaginatedResponse<Knowledge>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getKnowledges(authToken, pageSize)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch knowledges: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves knowledge items from a specific URL (for pagination).
     *
     * @param token The authentication token.
     * @param url The full URL for the next page.
     * @return A Result containing either the paginated response or an Exception.
     */
    suspend fun getKnowledgesFromUrl(token: String, url: String): Result<PaginatedResponse<Knowledge>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getKnowledgesFromUrl(authToken, url)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch knowledges: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the details of a specific knowledge item by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the knowledge item.
     * @return A Result containing either the knowledge item details or an Exception.
     */
    suspend fun getKnowledgeDetails(token: String, shortUuid: String): Result<Knowledge> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getKnowledgeDetails(authToken, shortUuid)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch knowledge details: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Creates a new knowledge item.
     *
     * @param token The authentication token.
     * @param title The title of the knowledge item.
     * @param body The content body of the knowledge item.
     * @return A Result containing either the created knowledge item or an Exception.
     */
    suspend fun createKnowledge(token: String, title: String, body: String): Result<Knowledge> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val knowledgeRequest = mapOf(
                    "title" to title,
                    "body" to body
                )
                val response = apiService.createKnowledge(authToken, knowledgeRequest)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to create knowledge: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Deletes a knowledge item by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the knowledge item to delete.
     * @return A Result indicating success or failure.
     */
    suspend fun deleteKnowledge(token: String, shortUuid: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.deleteKnowledge(authToken, shortUuid)

                if (response.isSuccessful) {
                    Result.success(Unit)
                } else {
                    Result.failure(Exception("Failed to delete knowledge: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Updates a knowledge item by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the knowledge item to update.
     * @param title The updated title of the knowledge item.
     * @param body The updated content body of the knowledge item.
     * @return A Result containing either the updated knowledge item or an Exception.
     */
    suspend fun updateKnowledge(token: String, shortUuid: String, title: String, body: String): Result<Knowledge> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val knowledgeRequest = mapOf(
                    "title" to title,
                    "body" to body
                )
                val response = apiService.updateKnowledge(authToken, shortUuid, knowledgeRequest)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to update knowledge: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
}
