package ir.rahavardit.ariel.data

import android.content.Context
import android.content.SharedPreferences
import ir.rahavardit.ariel.data.model.User
import com.google.gson.Gson

/**
 * Manages user session data including authentication token and user information.
 */
class SessionManager(context: Context) {

    private var prefs: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()

    companion object {
        private const val PREF_NAME = "ArielAppPrefs"
        private const val KEY_TOKEN = "user_token"
        private const val KEY_USER = "user_data"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
    }

    /**
     * Saves the authentication token to SharedPreferences.
     *
     * @param token The authentication token to save.
     */
    fun saveAuthToken(token: String) {
        val editor = prefs.edit()
        editor.putString(KEY_TOKEN, token)
        editor.apply()
    }

    /**
     * Retrieves the saved authentication token.
     *
     * @return The saved token or null if not found.
     */
    fun getAuthToken(): String? {
        return prefs.getString(KEY_TOKEN, null)
    }

    /**
     * Saves user information to SharedPreferences.
     *
     * @param user The User object to save.
     */
    fun saveUser(user: User) {
        val userJson = gson.toJson(user)
        val editor = prefs.edit()
        editor.putString(KEY_USER, userJson)
        editor.apply()
    }

    /**
     * Retrieves the saved user information.
     *
     * @return The User object or null if not found.
     */
    fun getUser(): User? {
        val userJson = prefs.getString(KEY_USER, null) ?: return null
        return try {
            gson.fromJson(userJson, User::class.java)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Sets the logged-in status of the user.
     *
     * @param isLoggedIn Whether the user is logged in.
     */
    fun setLoggedIn(isLoggedIn: Boolean) {
        val editor = prefs.edit()
        editor.putBoolean(KEY_IS_LOGGED_IN, isLoggedIn)
        editor.apply()
    }

    /**
     * Checks if the user is logged in.
     *
     * @return True if the user is logged in, false otherwise.
     */
    fun isLoggedIn(): Boolean {
        return prefs.getBoolean(KEY_IS_LOGGED_IN, false)
    }

    /**
     * Clears all session data when the user logs out.
     */
    fun clearSession() {
        val editor = prefs.edit()
        editor.clear()
        editor.apply()
    }

    /**
     * Checks if the current user is a superuser.
     *
     * @return True if the user is a superuser, false otherwise.
     */
    fun getUserIsSuperuser(): Boolean {
        return getUser()?.isSuperuser ?: false
    }

    /**
     * Checks if the current user is a limited admin.
     *
     * @return True if the user is a limited admin, false otherwise.
     */
    fun getUserIsLimitedAdmin(): Boolean {
        return getUser()?.isLimitedAdmin ?: false
    }
}
