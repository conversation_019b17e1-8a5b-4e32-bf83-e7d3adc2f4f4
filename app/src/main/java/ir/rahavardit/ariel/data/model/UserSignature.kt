package ir.rahavardit.ariel.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing a user signature in the system.
 *
 * @property id The unique identifier for the signature.
 * @property owner The owner of the signature.
 * @property body The content of the signature.
 * @property edited Whether the signature has been edited.
 * @property shortUuid The short UUID of the signature.
 * @property created The creation timestamp of the signature.
 * @property updated The last update timestamp of the signature.
 */
data class UserSignature(
    val id: Int,
    val owner: Author,
    val body: String,
    val edited: <PERSON><PERSON><PERSON>,
    @SerializedName("short_uuid") val shortUuid: String,
    val created: String,
    val updated: String
)
