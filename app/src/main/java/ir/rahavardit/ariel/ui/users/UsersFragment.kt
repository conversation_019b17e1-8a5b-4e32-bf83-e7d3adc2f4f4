package ir.rahavardit.ariel.ui.users

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.databinding.FragmentUsersBinding

/**
 * Fragment for displaying a list of users.
 * This page is only accessible to superusers.
 */
class UsersFragment : Fragment() {

    private var _binding: FragmentUsersBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: UsersViewModel
    private lateinit var userAdapter: UserAdapter
    private lateinit var sessionManager: SessionManager
    private val TAG = "UsersFragment"

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(UsersViewModel::class.java)
        _binding = FragmentUsersBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())

        // Check if user is a superuser
        val user = sessionManager.getUser()
        if (user == null || !user.isSuperuser) {
            // Only superusers can access this page, navigate back
            Toast.makeText(
                requireContext(),
                "Only administrators can access the Users page",
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        setupRecyclerView()
        observeViewModel()
        fetchUsers()
    }

    /**
     * Sets up the RecyclerView for displaying users.
     */
    private fun setupRecyclerView() {
        userAdapter = UserAdapter(viewModel, viewLifecycleOwner)

        binding.recyclerUsers.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = userAdapter
        }
    }

    /**
     * Observes changes in the ViewModel.
     */
    private fun observeViewModel() {
        viewModel.users.observe(viewLifecycleOwner) { users ->
            userAdapter.submitList(users)

            // Show empty view if the list is empty
            binding.tvEmpty.visibility = if (users.isEmpty()) View.VISIBLE else View.GONE
            binding.recyclerUsers.visibility = if (users.isEmpty()) View.GONE else View.VISIBLE
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE

            if (isLoading) {
                binding.tvError.visibility = View.GONE
                binding.tvEmpty.visibility = View.GONE
            }
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                binding.tvError.text = errorMessage
                binding.tvError.visibility = View.VISIBLE
                binding.recyclerUsers.visibility = View.GONE
                binding.tvEmpty.visibility = View.GONE
            } else {
                binding.tvError.visibility = View.GONE
            }
        }
    }

    /**
     * Fetches users from the API.
     */
    private fun fetchUsers() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchUsers(token)
        } else {
            Log.e(TAG, "No auth token found")
            binding.tvError.text = getString(R.string.users__error_authentication)
            binding.tvError.visibility = View.VISIBLE
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
