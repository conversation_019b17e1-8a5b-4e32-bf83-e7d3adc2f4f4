package ir.rahavardit.ariel.ui.editknowledge

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.Knowledge
import ir.rahavardit.ariel.data.repository.KnowledgeRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the edit knowledge screen.
 */
class EditKnowledgeViewModel : ViewModel() {

    private val knowledgeRepository = KnowledgeRepository()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _knowledgeUpdateResult = MutableLiveData<KnowledgeUpdateResult>()
    val knowledgeUpdateResult: LiveData<KnowledgeUpdateResult> = _knowledgeUpdateResult

    /**
     * Updates an existing knowledge item.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the knowledge item to update.
     * @param title The updated title of the knowledge item.
     * @param body The updated content body of the knowledge item.
     */
    fun updateKnowledge(token: String, shortUuid: String, title: String, body: String) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val result = knowledgeRepository.updateKnowledge(token, shortUuid, title, body)

                result.fold(
                    onSuccess = { knowledge ->
                        _knowledgeUpdateResult.value = KnowledgeUpdateResult.Success(knowledge)
                    },
                    onFailure = { exception ->
                        _knowledgeUpdateResult.value = KnowledgeUpdateResult.Error(
                            exception.message ?: "Failed to update knowledge"
                        )
                    }
                )
            } catch (e: Exception) {
                _knowledgeUpdateResult.value = KnowledgeUpdateResult.Error(
                    e.message ?: "Unknown error occurred"
                )
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Validates the input fields for updating a knowledge item.
     *
     * @param title The title of the knowledge item.
     * @param body The content body of the knowledge item.
     * @return A KnowledgeValidationResult indicating whether the inputs are valid.
     */
    fun validateInputs(title: String, body: String): KnowledgeValidationResult {
        val isTitleValid = title.isNotBlank()
        val isBodyValid = body.isNotBlank()

        return KnowledgeValidationResult(isTitleValid, isBodyValid)
    }

    /**
     * Clears any error messages.
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * Sealed class representing the result of updating a knowledge item.
     */
    sealed class KnowledgeUpdateResult {
        data class Success(val knowledge: Knowledge) : KnowledgeUpdateResult()
        data class Error(val errorMessage: String) : KnowledgeUpdateResult()
    }
}

/**
 * Data class representing the validation result for a knowledge item.
 *
 * @property isTitleValid Whether the title is valid.
 * @property isBodyValid Whether the body is valid.
 */
data class KnowledgeValidationResult(
    val isTitleValid: Boolean,
    val isBodyValid: Boolean
)
