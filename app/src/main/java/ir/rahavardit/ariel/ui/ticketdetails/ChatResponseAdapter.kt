package ir.rahavardit.ariel.ui.ticketdetails

import android.content.Intent
import android.net.Uri
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.model.TicketResponse
import ir.rahavardit.ariel.data.model.UserSignature
import ir.rahavardit.ariel.data.repository.TicketRepository
import ir.rahavardit.ariel.databinding.ItemChatMessageLeftBinding
import ir.rahavardit.ariel.databinding.ItemChatMessageRightBinding
import ir.rahavardit.ariel.utils.HtmlRenderer
import kotlinx.coroutines.launch

/**
 * Adapter for displaying ticket responses in a chat-style layout.
 * Messages from the ticket author are displayed on the right,
 * messages from other users are displayed on the left.
 */
class ChatResponseAdapter(
    private val lifecycleScope: LifecycleCoroutineScope,
    private val ticketAuthorUsername: String
) : ListAdapter<TicketResponse, RecyclerView.ViewHolder>(TicketResponseDiffCallback()) {

    companion object {
        private const val VIEW_TYPE_LEFT = 0  // Messages from other users
        private const val VIEW_TYPE_RIGHT = 1 // Messages from ticket author
    }

    private val ticketRepository = TicketRepository()

    override fun getItemViewType(position: Int): Int {
        val response = getItem(position)
        return if (response.author.username == ticketAuthorUsername) {
            VIEW_TYPE_RIGHT
        } else {
            VIEW_TYPE_LEFT
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            VIEW_TYPE_LEFT -> {
                val binding = ItemChatMessageLeftBinding.inflate(inflater, parent, false)
                LeftMessageViewHolder(binding)
            }
            VIEW_TYPE_RIGHT -> {
                val binding = ItemChatMessageRightBinding.inflate(inflater, parent, false)
                RightMessageViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val response = getItem(position)
        when (holder) {
            is LeftMessageViewHolder -> holder.bind(response)
            is RightMessageViewHolder -> holder.bind(response)
        }
    }

    /**
     * ViewHolder for left-aligned messages (from other users).
     */
    inner class LeftMessageViewHolder(private val binding: ItemChatMessageLeftBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(response: TicketResponse) {
            binding.apply {
                // Set basic information
                tvResponseId.text = response.shortUuid
                tvResponseAuthor.text = response.author.username
                tvResponseDate.text = response.createdJalali

                // Set message content with HTML support
                HtmlRenderer.applyToTextView(tvResponseMessage, response.message)

                // Check if user has signature (for superusers and limited admins)
                if (response.author.isSuperuser || response.author.isLimitedAdmin) {
                    fetchUserSignature(response.author.shortUuid)
                } else {
                    tvUserSignature.visibility = View.GONE
                }

                // Handle file attachment
                if (response.hasFile && response.file != null) {
                    layoutResponseFile.visibility = View.VISIBLE
                    tvResponseFileName.text = response.file.substringAfterLast('/')

                    btnResponseDownloadFile.setOnClickListener {
                        openFileUrl(response.file)
                    }
                } else {
                    layoutResponseFile.visibility = View.GONE
                }
            }
        }

        private fun fetchUserSignature(shortUuid: String) {
            val sessionManager = SessionManager(binding.root.context)
            val token = sessionManager.getAuthToken() ?: return

            lifecycleScope.launch {
                try {
                    val result = ticketRepository.getUserSignature(token, shortUuid)

                    result.fold(
                        onSuccess = { signature ->
                            displaySignature(signature)
                        },
                        onFailure = {
                            // Silently fail - signature is optional
                        }
                    )
                } catch (e: Exception) {
                    // Silently fail - signature is optional
                }
            }
        }

        private fun displaySignature(signature: UserSignature) {
            binding.apply {
                if (signature.body.isNotBlank()) {
                    HtmlRenderer.applyToTextView(tvUserSignature, signature.body)
                    tvUserSignature.visibility = View.VISIBLE
                } else {
                    tvUserSignature.visibility = View.GONE
                }
            }
        }

        private fun openFileUrl(fileUrl: String) {
            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(fileUrl))
                binding.root.context.startActivity(intent)
            } catch (e: Exception) {
                Toast.makeText(
                    binding.root.context,
                    binding.root.context.getString(R.string.error_opening_file),
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    /**
     * ViewHolder for right-aligned messages (from ticket author).
     */
    inner class RightMessageViewHolder(private val binding: ItemChatMessageRightBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(response: TicketResponse) {
            binding.apply {
                // Set basic information (no author for right side)
                tvResponseId.text = response.shortUuid
                tvResponseDate.text = response.createdJalali

                // Set message content with HTML support
                HtmlRenderer.applyToTextView(tvResponseMessage, response.message)

                // Check if user has signature (for superusers and limited admins)
                if (response.author.isSuperuser || response.author.isLimitedAdmin) {
                    fetchUserSignature(response.author.shortUuid)
                } else {
                    tvUserSignature.visibility = View.GONE
                }

                // Handle file attachment
                if (response.hasFile && response.file != null) {
                    layoutResponseFile.visibility = View.VISIBLE
                    tvResponseFileName.text = response.file.substringAfterLast('/')

                    btnResponseDownloadFile.setOnClickListener {
                        openFileUrl(response.file)
                    }
                } else {
                    layoutResponseFile.visibility = View.GONE
                }
            }
        }

        private fun fetchUserSignature(shortUuid: String) {
            val sessionManager = SessionManager(binding.root.context)
            val token = sessionManager.getAuthToken() ?: return

            lifecycleScope.launch {
                try {
                    val result = ticketRepository.getUserSignature(token, shortUuid)

                    result.fold(
                        onSuccess = { signature ->
                            displaySignature(signature)
                        },
                        onFailure = {
                            // Silently fail - signature is optional
                        }
                    )
                } catch (e: Exception) {
                    // Silently fail - signature is optional
                }
            }
        }

        private fun displaySignature(signature: UserSignature) {
            binding.apply {
                if (signature.body.isNotBlank()) {
                    HtmlRenderer.applyToTextView(tvUserSignature, signature.body)
                    tvUserSignature.visibility = View.VISIBLE
                } else {
                    tvUserSignature.visibility = View.GONE
                }
            }
        }

        private fun openFileUrl(fileUrl: String) {
            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(fileUrl))
                binding.root.context.startActivity(intent)
            } catch (e: Exception) {
                Toast.makeText(
                    binding.root.context,
                    binding.root.context.getString(R.string.error_opening_file),
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    /**
     * DiffUtil callback for efficient list updates.
     */
    class TicketResponseDiffCallback : DiffUtil.ItemCallback<TicketResponse>() {
        override fun areItemsTheSame(oldItem: TicketResponse, newItem: TicketResponse): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: TicketResponse, newItem: TicketResponse): Boolean {
            return oldItem == newItem
        }
    }
}
