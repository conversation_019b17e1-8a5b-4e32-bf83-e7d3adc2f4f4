package ir.rahavardit.ariel.ui.newevent

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.OpenableColumns
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.model.EventGroup
import ir.rahavardit.ariel.databinding.FragmentNewEventBinding

/**
 * Fragment for creating a new event.
 */
class NewEventFragment : Fragment() {

    private var _binding: FragmentNewEventBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: NewEventViewModel
    private lateinit var sessionManager: SessionManager

    private var selectedGroupId: Int? = null

    // File picker launcher
    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                handleSelectedFile(uri)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(NewEventViewModel::class.java)
        _binding = FragmentNewEventBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())

        // Check if user is a superuser
        val user = sessionManager.getUser()
        if (user == null || !user.isSuperuser) {
            // Only superusers can create events, navigate back
            Toast.makeText(
                requireContext(),
                "Only administrators can create events",
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        setupListeners()
        observeViewModel()
        fetchGroups()
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        // Set up file selection button
        binding.btnSelectFile.setOnClickListener {
            openFilePicker()
        }

        // Set up submit button
        binding.btnSubmit.setOnClickListener {
            val title = binding.etEventTitle.text.toString().trim()
            val body = binding.etEventBody.text.toString().trim()

            val validationResult = viewModel.validateInputs(title, body, selectedGroupId)

            when {
                !validationResult.isTitleValid -> {
                    binding.tilEventTitle.error = getString(R.string.please_enter_title)
                }
                !validationResult.isBodyValid -> {
                    binding.tilEventBody.error = getString(R.string.please_enter_body)
                }
                !validationResult.isGroupValid -> {
                    binding.tilEventGroup.error = getString(R.string.please_select_group)
                }
                else -> {
                    // Clear any previous errors
                    binding.tilEventTitle.error = null
                    binding.tilEventBody.error = null
                    binding.tilEventGroup.error = null

                    // Create event
                    val token = sessionManager.getAuthToken()
                    if (token != null) {
                        viewModel.createEvent(
                            token,
                            title,
                            body,
                            selectedGroupId!!,
                            requireContext() // Pass context for file operations
                        )
                    } else {
                        showError(getString(R.string.authentication_token_not_found))
                    }
                }
            }
        }

        // Set up group dropdown
        binding.dropdownGroup.setOnItemClickListener { _, _, position, _ ->
            val groups = viewModel.groups.value
            if (groups != null && position < groups.size) {
                selectedGroupId = groups[position].id
                binding.tilEventGroup.error = null
            }
        }
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnSubmit.isEnabled = !isLoading
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                showError(errorMessage)
            }
        }

        viewModel.groups.observe(viewLifecycleOwner) { groups ->
            setupGroupsDropdown(groups)
        }

        viewModel.selectedFile.observe(viewLifecycleOwner) { uri ->
            if (uri != null) {
                val fileName = getFileName(uri)
                binding.tvSelectedFile.text = getString(R.string.file_selected, fileName)
                binding.tvSelectedFile.visibility = View.VISIBLE
            } else {
                binding.tvSelectedFile.visibility = View.GONE
            }
        }

        viewModel.eventCreationResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is NewEventViewModel.EventCreationResult.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.event_created_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Navigate to event details
                    val bundle = Bundle().apply {
                        putString("shortUuid", result.event.shortUuid)
                    }
                    findNavController().navigate(
                        R.id.action_nav_new_event_to_eventDetailsFragment,
                        bundle
                    )
                }
                is NewEventViewModel.EventCreationResult.Error -> {
                    showError(result.errorMessage)
                }
            }
        }
    }

    /**
     * Fetches the list of available groups.
     */
    private fun fetchGroups() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchGroups(token)
        } else {
            showError(getString(R.string.authentication_token_not_found))
        }
    }

    /**
     * Sets up the groups dropdown with the provided list of groups.
     *
     * @param groups The list of groups to display in the dropdown.
     */
    private fun setupGroupsDropdown(groups: List<EventGroup>) {
        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            groups.map { it.name }
        )
        binding.dropdownGroup.setAdapter(adapter)
    }

    /**
     * Opens the file picker to select a file.
     */
    private fun openFilePicker() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "*/*"
        }
        filePickerLauncher.launch(intent)
    }

    /**
     * Handles the selected file.
     *
     * @param uri The URI of the selected file.
     */
    private fun handleSelectedFile(uri: Uri) {
        try {
            // Check file size (max 5MB)
            val fileSize = getFileSize(uri)
            if (fileSize > 5 * 1024 * 1024) { // 5MB in bytes
                Toast.makeText(requireContext(), getString(R.string.file_too_large), Toast.LENGTH_SHORT).show()
                return
            }

            // Set the selected file in the ViewModel
            viewModel.setSelectedFile(uri)
        } catch (e: Exception) {
            Toast.makeText(requireContext(), getString(R.string.error_selecting_file), Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Gets the size of a file from its URI.
     *
     * @param uri The URI of the file.
     * @return The size of the file in bytes.
     */
    private fun getFileSize(uri: Uri): Long {
        val cursor = requireContext().contentResolver.query(uri, null, null, null, null)
        return cursor?.use {
            val sizeIndex = it.getColumnIndex(OpenableColumns.SIZE)
            it.moveToFirst()
            it.getLong(sizeIndex)
        } ?: 0
    }

    /**
     * Gets the name of a file from its URI.
     *
     * @param uri The URI of the file.
     * @return The name of the file.
     */
    private fun getFileName(uri: Uri): String {
        val cursor = requireContext().contentResolver.query(uri, null, null, null, null)
        return cursor?.use {
            val nameIndex = it.getColumnIndex(OpenableColumns.DISPLAY_NAME)
            it.moveToFirst()
            it.getString(nameIndex)
        } ?: "Unknown file"
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to show.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
