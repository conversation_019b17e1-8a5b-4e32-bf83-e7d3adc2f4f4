package ir.rahavardit.ariel.ui.ticketdetails

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.data.model.TicketChange
import ir.rahavardit.ariel.databinding.ItemTicketChangeBinding

/**
 * Adapter for displaying ticket changes in a RecyclerView.
 */


class TicketChangeAdapter : ListAdapter<TicketChange, TicketChangeAdapter.TicketChangeViewHolder>(TicketChangeDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TicketChangeViewHolder {
        val binding = ItemTicketChangeBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return TicketChangeViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TicketChangeViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * ViewHolder for a ticket change item.
     */
    inner class TicketChangeViewHolder(private val binding: ItemTicketChangeBinding) :
        RecyclerView.ViewHolder(binding.root) {

        /**
         * Binds a ticket change to the ViewHolder.
         *
         * @param change The ticket change to bind.
         */
        fun bind(change: TicketChange) {
            binding.apply {
                tvChangeAction.text = change.action
                tvChangeUsername.text = change.username
                tvChangeDate.text = change.dateJalali
            }
        }
    }

    /**
     * DiffUtil callback for comparing ticket changes.
     */
    private class TicketChangeDiffCallback : DiffUtil.ItemCallback<TicketChange>() {
        override fun areItemsTheSame(oldItem: TicketChange, newItem: TicketChange): Boolean {
            // Since we don't have a unique ID, compare by all fields
            return oldItem.userId == newItem.userId && 
                   oldItem.action == newItem.action && 
                   oldItem.dateJalali == newItem.dateJalali
        }

        override fun areContentsTheSame(oldItem: TicketChange, newItem: TicketChange): Boolean {
            return oldItem == newItem
        }
    }
}
