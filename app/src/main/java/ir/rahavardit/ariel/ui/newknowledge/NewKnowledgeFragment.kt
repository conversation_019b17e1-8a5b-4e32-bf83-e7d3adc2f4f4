package ir.rahavardit.ariel.ui.newknowledge

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.databinding.FragmentNewKnowledgeBinding

/**
 * Fragment for creating a new knowledge item.
 */
class NewKnowledgeFragment : Fragment() {

    private var _binding: FragmentNewKnowledgeBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: NewKnowledgeViewModel
    private lateinit var sessionManager: SessionManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(NewKnowledgeViewModel::class.java)
        _binding = FragmentNewKnowledgeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())

        // Check if user is a superuser
        val user = sessionManager.getUser()
        if (user == null || !user.isSuperuser) {
            // Only superusers can create knowledge items, navigate back
            Toast.makeText(
                requireContext(),
                "Only administrators can create knowledge items",
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        setupListeners()
        observeViewModel()
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        binding.btnSubmit.setOnClickListener {
            val title = binding.etKnowledgeTitle.text.toString().trim()
            val body = binding.etKnowledgeBody.text.toString().trim()

            val validationResult = viewModel.validateInputs(title, body)

            when {
                !validationResult.isTitleValid -> {
                    binding.tilKnowledgeTitle.error = getString(R.string.please_enter_title)
                }
                !validationResult.isBodyValid -> {
                    binding.tilKnowledgeBody.error = getString(R.string.please_enter_body)
                }
                else -> {
                    // Clear any previous errors
                    binding.tilKnowledgeTitle.error = null
                    binding.tilKnowledgeBody.error = null

                    // Create knowledge
                    val token = sessionManager.getAuthToken()
                    if (token != null) {
                        viewModel.createKnowledge(token, title, body)
                    } else {
                        showError(getString(R.string.authentication_token_not_found))
                    }
                }
            }
        }
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnSubmit.isEnabled = !isLoading
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                showError(errorMessage)
            } else {
                binding.tvError.visibility = View.GONE
            }
        }

        viewModel.knowledgeCreationResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is NewKnowledgeViewModel.KnowledgeCreationResult.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.knowledge_created_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Navigate to knowledge details
                    val bundle = Bundle().apply {
                        putString("shortUuid", result.knowledge.shortUuid)
                    }
                    findNavController().navigate(
                        R.id.action_nav_new_knowledge_to_knowledgeDetailsFragment,
                        bundle
                    )
                }
                is NewKnowledgeViewModel.KnowledgeCreationResult.Error -> {
                    showError(result.errorMessage)
                }
            }
        }
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to show.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
