package ir.rahavardit.ariel.ui.editevent

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.OpenableColumns
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.model.Event
import ir.rahavardit.ariel.data.model.EventGroup
import ir.rahavardit.ariel.databinding.FragmentEditEventBinding

/**
 * Fragment for editing an existing event.
 */
class EditEventFragment : Fragment() {

    private var _binding: FragmentEditEventBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: EditEventViewModel
    private lateinit var sessionManager: SessionManager
    private var shortUuid: String? = null
    private var event: Event? = null
    private var selectedGroupId: Int? = null

    // File picker launcher
    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                handleSelectedFile(uri)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(EditEventViewModel::class.java)
        _binding = FragmentEditEventBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        shortUuid = arguments?.getString("shortUuid")
        event = arguments?.getParcelable("event")

        // Check if user is a superuser
        if (!sessionManager.getUserIsSuperuser()) {
            // Only superusers can edit events, navigate back
            Toast.makeText(
                requireContext(),
                "Only administrators can edit events",
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        // Check if we have the event data
        if (event == null) {
            Toast.makeText(
                requireContext(),
                getString(R.string.event_data_not_found),
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        // Populate the form with the event data
        populateForm()
        setupListeners()
        observeViewModel()
        fetchGroups()
    }

    /**
     * Populates the form with the event data.
     */
    private fun populateForm() {
        event?.let {
            binding.etEventTitle.setText(it.title)
            binding.etEventBody.setText(it.body)
            selectedGroupId = it.group.id

            // Show existing file if present
            if (it.hasFile && it.file != null) {
                val fileName = getFileNameFromUrl(it.file)
                binding.tvSelectedFile.text = getString(R.string.current_file, fileName)
                binding.tvSelectedFile.visibility = View.VISIBLE
            }
        }
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        // Set up file selection button
        binding.btnSelectFile.setOnClickListener {
            openFilePicker()
        }

        // Set up submit button
        binding.btnSubmit.setOnClickListener {
            val title = binding.etEventTitle.text.toString().trim()
            val body = binding.etEventBody.text.toString().trim()

            val validationResult = viewModel.validateInputs(title, body, selectedGroupId)

            when {
                !validationResult.isTitleValid -> {
                    binding.tilEventTitle.error = getString(R.string.please_enter_title)
                }
                !validationResult.isBodyValid -> {
                    binding.tilEventBody.error = getString(R.string.please_enter_body)
                }
                !validationResult.isGroupValid -> {
                    binding.tilEventGroup.error = getString(R.string.please_select_group)
                }
                else -> {
                    // Clear any previous errors
                    binding.tilEventTitle.error = null
                    binding.tilEventBody.error = null
                    binding.tilEventGroup.error = null

                    // Update event
                    val token = sessionManager.getAuthToken()
                    if (token != null && shortUuid != null) {
                        viewModel.updateEvent(
                            token,
                            shortUuid!!,
                            title,
                            body,
                            selectedGroupId!!,
                            requireContext() // Pass context for file operations
                        )
                    } else if (token == null) {
                        showError(getString(R.string.authentication_token_not_found))
                    } else {
                        showError(getString(R.string.event_id_not_found))
                    }
                }
            }
        }
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(viewLifecycleOwner) { error ->
            error?.let {
                showError(it)
            }
        }

        viewModel.groups.observe(viewLifecycleOwner) { groups ->
            setupGroupsDropdown(groups)
        }

        viewModel.selectedFile.observe(viewLifecycleOwner) { uri ->
            if (uri != null) {
                val fileName = getFileNameFromUri(uri)
                binding.tvSelectedFile.text = getString(R.string.file_selected, fileName)
                binding.tvSelectedFile.visibility = View.VISIBLE
            } else {
                binding.tvSelectedFile.visibility = View.GONE
            }
        }

        viewModel.eventUpdateResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is EditEventViewModel.EventUpdateResult.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.event_updated_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Navigate back to event details
                    val bundle = Bundle().apply {
                        putString("shortUuid", result.event.shortUuid)
                    }
                    findNavController().navigate(
                        R.id.action_editEventFragment_to_eventDetailsFragment,
                        bundle
                    )
                }
                is EditEventViewModel.EventUpdateResult.Error -> {
                    showError(result.errorMessage)
                }
            }
        }
    }

    /**
     * Fetches the list of available groups.
     */
    private fun fetchGroups() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchGroups(token)
        } else {
            showError(getString(R.string.authentication_token_not_found))
        }
    }

    /**
     * Sets up the groups dropdown.
     *
     * @param groups The list of available groups.
     */
    private fun setupGroupsDropdown(groups: List<EventGroup>) {
        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            groups.map { it.name }
        )

        binding.dropdownGroup.setAdapter(adapter)
        binding.dropdownGroup.setOnItemClickListener { _, _, position, _ ->
            selectedGroupId = groups[position].id
        }

        // Set the current group
        event?.let { currentEvent ->
            val currentGroupIndex = groups.indexOfFirst { it.id == currentEvent.group.id }
            if (currentGroupIndex != -1) {
                binding.dropdownGroup.setText(groups[currentGroupIndex].name, false)
                selectedGroupId = groups[currentGroupIndex].id
            }
        }
    }

    /**
     * Opens the file picker.
     */
    private fun openFilePicker() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "*/*"
        }
        filePickerLauncher.launch(intent)
    }

    /**
     * Handles the selected file.
     *
     * @param uri The URI of the selected file.
     */
    private fun handleSelectedFile(uri: Uri) {
        try {
            viewModel.setSelectedFile(uri)
        } catch (e: Exception) {
            Toast.makeText(
                requireContext(),
                getString(R.string.error_selecting_file),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    /**
     * Gets the file name from a URI.
     *
     * @param uri The URI to get the file name from.
     * @return The file name.
     */
    private fun getFileNameFromUri(uri: Uri): String {
        val cursor = requireContext().contentResolver.query(uri, null, null, null, null)
        val nameIndex = cursor?.getColumnIndex(OpenableColumns.DISPLAY_NAME)
        cursor?.moveToFirst()
        val fileName = cursor?.getString(nameIndex ?: 0) ?: "Unknown file"
        cursor?.close()
        return fileName
    }

    /**
     * Gets the file name from a URL.
     *
     * @param url The URL to get the file name from.
     * @return The file name.
     */
    private fun getFileNameFromUrl(url: String): String {
        return try {
            val uri = Uri.parse(url)
            val path = uri.path
            if (path != null && path.contains('/')) {
                path.substringAfterLast('/')
            } else {
                "Attached file"
            }
        } catch (e: Exception) {
            "Attached file"
        }
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to show.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
