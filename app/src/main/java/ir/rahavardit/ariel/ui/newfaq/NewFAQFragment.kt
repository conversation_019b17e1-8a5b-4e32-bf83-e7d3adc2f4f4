package ir.rahavardit.ariel.ui.newfaq

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.databinding.FragmentNewFaqBinding

/**
 * Fragment for creating a new FAQ item.
 */
class NewFAQFragment : Fragment() {

    private var _binding: FragmentNewFaqBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: NewFAQViewModel
    private lateinit var sessionManager: SessionManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(NewFAQViewModel::class.java)
        _binding = FragmentNewFaqBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())

        // Check if user is a superuser
        val user = sessionManager.getUser()
        if (user == null || !user.isSuperuser) {
            // Only superusers can create FAQ items, navigate back
            Toast.makeText(
                requireContext(),
                "Only administrators can create FAQ items",
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        setupListeners()
        observeViewModel()
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        binding.btnSubmit.setOnClickListener {
            val title = binding.etFaqTitle.text.toString().trim()
            val body = binding.etFaqBody.text.toString().trim()

            val validationResult = viewModel.validateInputs(title, body)

            when {
                !validationResult.isTitleValid -> {
                    binding.tilFaqTitle.error = getString(R.string.please_enter_title)
                }
                !validationResult.isBodyValid -> {
                    binding.tilFaqBody.error = getString(R.string.please_enter_body)
                }
                else -> {
                    // Clear any previous errors
                    binding.tilFaqTitle.error = null
                    binding.tilFaqBody.error = null

                    // Create FAQ
                    val token = sessionManager.getAuthToken()
                    if (token != null) {
                        viewModel.createFAQ(token, title, body)
                    } else {
                        showError(getString(R.string.authentication_token_not_found))
                    }
                }
            }
        }
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnSubmit.isEnabled = !isLoading
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                showError(errorMessage)
            } else {
                binding.tvError.visibility = View.GONE
            }
        }

        viewModel.faqCreationResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is NewFAQViewModel.FAQCreationResult.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.faq_created_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Navigate to FAQ details
                    val bundle = Bundle().apply {
                        putString("shortUuid", result.faq.shortUuid)
                    }
                    findNavController().navigate(
                        R.id.action_nav_new_faq_to_faqDetailsFragment,
                        bundle
                    )
                }
                is NewFAQViewModel.FAQCreationResult.Error -> {
                    showError(result.errorMessage)
                }
            }
        }
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to show.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
