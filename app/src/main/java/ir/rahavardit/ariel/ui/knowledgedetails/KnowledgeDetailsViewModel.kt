package ir.rahavardit.ariel.ui.knowledgedetails

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.Knowledge
import ir.rahavardit.ariel.data.repository.KnowledgeRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the knowledge details screen that handles knowledge detail operations.
 */
class KnowledgeDetailsViewModel : ViewModel() {

    private val knowledgeRepository = KnowledgeRepository()

    private val _knowledgeState = MutableLiveData<KnowledgeState>()
    val knowledgeState: LiveData<KnowledgeState> = _knowledgeState

    private val _deleteState = MutableLiveData<DeleteState>()
    val deleteState: LiveData<DeleteState> = _deleteState

    private val _currentKnowledge = MutableLiveData<Knowledge>()
    val currentKnowledge: LiveData<Knowledge> = _currentKnowledge

    /**
     * Fetches the details of a specific knowledge item by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the knowledge item.
     */
    fun fetchKnowledgeDetails(token: String, shortUuid: String) {
        _knowledgeState.value = KnowledgeState.Loading

        viewModelScope.launch {
            try {
                val result = knowledgeRepository.getKnowledgeDetails(token, shortUuid)

                result.fold(
                    onSuccess = { knowledge ->
                        _knowledgeState.value = KnowledgeState.Success(knowledge)
                        _currentKnowledge.value = knowledge
                    },
                    onFailure = { exception ->
                        _knowledgeState.value = KnowledgeState.Error(exception.message ?: "Unknown error occurred")
                    }
                )
            } catch (e: Exception) {
                _knowledgeState.value = KnowledgeState.Error(e.message ?: "Unknown error occurred")
            }
        }
    }

    /**
     * Deletes a knowledge item by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the knowledge item to delete.
     */
    fun deleteKnowledge(token: String, shortUuid: String) {
        _deleteState.value = DeleteState.Loading

        viewModelScope.launch {
            try {
                val result = knowledgeRepository.deleteKnowledge(token, shortUuid)

                result.fold(
                    onSuccess = {
                        _deleteState.value = DeleteState.Success
                    },
                    onFailure = { exception ->
                        _deleteState.value = DeleteState.Error(exception.message ?: "Unknown error occurred")
                    }
                )
            } catch (e: Exception) {
                _deleteState.value = DeleteState.Error(e.message ?: "Unknown error occurred")
            }
        }
    }

    /**
     * Sealed class representing the state of the knowledge details.
     */
    sealed class KnowledgeState {
        /**
         * Represents the loading state.
         */
        object Loading : KnowledgeState()

        /**
         * Represents a successful state with knowledge details.
         *
         * @property knowledge The knowledge details.
         */
        data class Success(val knowledge: Knowledge) : KnowledgeState()

        /**
         * Represents an error state.
         *
         * @property errorMessage The error message.
         */
        data class Error(val errorMessage: String) : KnowledgeState()
    }

    /**
     * Sealed class representing the state of the delete operation.
     */
    sealed class DeleteState {
        /**
         * Represents the loading state.
         */
        object Loading : DeleteState()

        /**
         * Represents a successful delete operation.
         */
        object Success : DeleteState()

        /**
         * Represents an error state.
         *
         * @property errorMessage The error message.
         */
        data class Error(val errorMessage: String) : DeleteState()
    }
}
