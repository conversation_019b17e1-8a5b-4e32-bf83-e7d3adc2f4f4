package ir.rahavardit.ariel.ui.ticketdetails

import android.content.Intent
import android.net.Uri
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.model.TicketResponse
import ir.rahavardit.ariel.data.model.UserSignature
import ir.rahavardit.ariel.data.repository.TicketRepository
import ir.rahavardit.ariel.databinding.ItemTicketResponseBinding
import ir.rahavardit.ariel.utils.HtmlRenderer
import kotlinx.coroutines.launch

/**
 * Adapter for displaying ticket responses (children) in a RecyclerView.
 *
 * @property lifecycleScope The lifecycle scope for launching coroutines.
 */
class TicketResponseAdapter(private val lifecycleScope: LifecycleCoroutineScope) :
    ListAdapter<TicketResponse, TicketResponseAdapter.TicketResponseViewHolder>(TicketResponseDiffCallback()) {

    private val ticketRepository = TicketRepository()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TicketResponseViewHolder {
        val binding = ItemTicketResponseBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return TicketResponseViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TicketResponseViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * ViewHolder for a ticket response item.
     */
    inner class TicketResponseViewHolder(private val binding: ItemTicketResponseBinding) :
        RecyclerView.ViewHolder(binding.root) {

        /**
         * Binds a ticket response to the ViewHolder.
         *
         * @param response The ticket response to bind.
         */
        fun bind(response: TicketResponse) {
            binding.apply {
                // Set basic information in a single row
                tvResponseId.text = response.shortUuid
                tvResponseAuthor.text = response.author.username
                tvResponseDate.text = response.createdJalali

                // Set message content with HTML support
                HtmlRenderer.applyToTextView(tvResponseMessage, response.message)

                // Reset signature view
                tvUserSignature.visibility = View.GONE
                tvUserSignature.text = ""

                // Highlight if the author is a superuser or limited admin
                if (response.author.isSuperuser || response.author.isLimitedAdmin) {
                    tvResponseAuthor.setTextColor(
                        ContextCompat.getColor(tvResponseAuthor.context, R.color.primary)
                    )
                    // ivAdminBadge.visibility = View.GONE // Remove the star

                    // Fetch and display signature for superuser
                    if (response.author.isSuperuser) {
                        fetchUserSignature(response.author.shortUuid)
                    }
                } else {
                    // Use secondary text color to match the date and short UUID
                    val typedArray = tvResponseAuthor.context.obtainStyledAttributes(intArrayOf(android.R.attr.textColorSecondary))
                    val textColor = typedArray.getColor(0, 0)
                    typedArray.recycle()
                    tvResponseAuthor.setTextColor(textColor)
                    // ivAdminBadge.visibility = View.GONE
                }

                // Handle file attachment for response
                if (response.hasFile && response.file != null) {
                    layoutResponseFile.visibility = View.VISIBLE
                    tvResponseFileName.text = response.file.substringAfterLast('/')

                    // Set up download button click listener
                    btnResponseDownloadFile.setOnClickListener {
                        openFileUrl(response.file)
                    }
                } else {
                    layoutResponseFile.visibility = View.GONE
                }
            }
        }

        /**
         * Fetches and displays the signature for a superuser.
         *
         * @param shortUuid The short UUID of the user whose signature to fetch.
         */
        private fun fetchUserSignature(shortUuid: String) {
            val sessionManager = SessionManager(binding.root.context)
            val token = sessionManager.getAuthToken() ?: return

            lifecycleScope.launch {
                try {
                    val result = ticketRepository.getUserSignature(token, shortUuid)

                    result.fold(
                        onSuccess = { signature ->
                            displaySignature(signature)
                        },
                        onFailure = { exception ->
                            // Silently fail - signature is optional
                            // Log error if needed
                        }
                    )
                } catch (e: Exception) {
                    // Silently fail - signature is optional
                    // Log error if needed
                }
            }
        }

        /**
         * Displays the user signature in the UI.
         *
         * @param signature The signature to display.
         */
        private fun displaySignature(signature: UserSignature) {
            binding.apply {
                if (signature.body.isNotBlank()) {
                    tvUserSignature.text = signature.body
                    tvUserSignature.visibility = View.VISIBLE
                } else {
                    tvUserSignature.visibility = View.GONE
                }
            }
        }

        /**
         * Extracts the file name from a URL.
         *
         * @param url The URL to extract the file name from.
         * @return The file name.
         */
        private fun getFileNameFromUrl(url: String): String {
            return url.substringAfterLast("/")
        }

        /**
         * Opens a URL in the browser.
         *
         * @param url The URL to open.
         */
        private fun openFileUrl(url: String) {
            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                binding.root.context.startActivity(intent)
            } catch (e: Exception) {
                Toast.makeText(binding.root.context, "Cannot open file: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * DiffUtil callback for comparing ticket responses.
     */
    private class TicketResponseDiffCallback : DiffUtil.ItemCallback<TicketResponse>() {
        override fun areItemsTheSame(oldItem: TicketResponse, newItem: TicketResponse): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: TicketResponse, newItem: TicketResponse): Boolean {
            return oldItem == newItem
        }
    }
}
