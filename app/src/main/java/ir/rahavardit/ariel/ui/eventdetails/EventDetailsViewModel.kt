package ir.rahavardit.ariel.ui.eventdetails

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.Event
import ir.rahavardit.ariel.data.repository.EventRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the event details screen that handles event detail operations.
 */
class EventDetailsViewModel : ViewModel() {

    private val eventRepository = EventRepository()

    private val _eventState = MutableLiveData<EventState>()
    val eventState: LiveData<EventState> = _eventState

    private val _deleteState = MutableLiveData<DeleteState>()
    val deleteState: LiveData<DeleteState> = _deleteState

    private val _currentEvent = MutableLiveData<Event>()
    val currentEvent: LiveData<Event> = _currentEvent

    /**
     * Fetches the details of a specific event by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the event.
     */
    fun fetchEventDetails(token: String, shortUuid: String) {
        _eventState.value = EventState.Loading

        viewModelScope.launch {
            try {
                val result = eventRepository.getEventDetails(token, shortUuid)

                result.fold(
                    onSuccess = { event ->
                        _currentEvent.value = event
                        _eventState.value = EventState.Success(event)
                    },
                    onFailure = { exception ->
                        _eventState.value = EventState.Error(exception.message ?: "Unknown error occurred")
                    }
                )
            } catch (e: Exception) {
                _eventState.value = EventState.Error(e.message ?: "Unknown error occurred")
            }
        }
    }

    /**
     * Deletes an event item by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the event item to delete.
     */
    fun deleteEvent(token: String, shortUuid: String) {
        _deleteState.value = DeleteState.Loading

        viewModelScope.launch {
            try {
                val result = eventRepository.deleteEvent(token, shortUuid)

                result.fold(
                    onSuccess = {
                        _deleteState.value = DeleteState.Success
                    },
                    onFailure = { exception ->
                        _deleteState.value = DeleteState.Error(exception.message ?: "Unknown error occurred")
                    }
                )
            } catch (e: Exception) {
                _deleteState.value = DeleteState.Error(e.message ?: "Unknown error occurred")
            }
        }
    }

    /**
     * Sealed class representing the state of the event details.
     */
    sealed class EventState {
        /**
         * Represents the loading state.
         */
        object Loading : EventState()

        /**
         * Represents a successful state with event details.
         *
         * @property event The event details.
         */
        data class Success(val event: Event) : EventState()

        /**
         * Represents an error state.
         *
         * @property errorMessage The error message.
         */
        data class Error(val errorMessage: String) : EventState()
    }

    /**
     * Sealed class representing the state of the delete operation.
     */
    sealed class DeleteState {
        /**
         * Represents the loading state.
         */
        object Loading : DeleteState()

        /**
         * Represents a successful delete operation.
         */
        object Success : DeleteState()

        /**
         * Represents an error state.
         *
         * @property errorMessage The error message.
         */
        data class Error(val errorMessage: String) : DeleteState()
    }
}
