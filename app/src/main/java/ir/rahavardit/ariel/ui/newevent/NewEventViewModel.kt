package ir.rahavardit.ariel.ui.newevent

import android.content.Context
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.Event
import ir.rahavardit.ariel.data.model.EventGroup
import ir.rahavardit.ariel.data.repository.NewEventRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the new event screen that handles event creation operations.
 */
class NewEventViewModel : ViewModel() {

    private val newEventRepository = NewEventRepository()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _groups = MutableLiveData<List<EventGroup>>()
    val groups: LiveData<List<EventGroup>> = _groups

    // Selected file
    private val _selectedFile = MutableLiveData<Uri?>()
    val selectedFile: LiveData<Uri?> = _selectedFile

    private val _eventCreationResult = MutableLiveData<EventCreationResult>()
    val eventCreationResult: LiveData<EventCreationResult> = _eventCreationResult

    /**
     * Fetches the list of available groups.
     *
     * @param token The authentication token.
     */
    fun fetchGroups(token: String) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val result = newEventRepository.getGroups(token)

                result.fold(
                    onSuccess = { groupsList ->
                        _groups.value = groupsList
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Failed to fetch groups"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "An unexpected error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Creates a new event.
     *
     * @param token The authentication token.
     * @param title The title of the event.
     * @param body The body content of the event.
     * @param groupId The ID of the group the event belongs to.
     * @param context The context to use for file operations (optional).
     */
    fun createEvent(
        token: String,
        title: String,
        body: String,
        groupId: Int,
        context: Context? = null
    ) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val fileUri = _selectedFile.value

                val result = if (fileUri != null && context != null) {
                    // Create event with file
                    newEventRepository.createEventWithFile(
                        token, title, body, groupId, fileUri, context
                    )
                } else {
                    // Create event without file
                    newEventRepository.createEvent(token, title, body, groupId)
                }

                result.fold(
                    onSuccess = { event ->
                        _eventCreationResult.value = EventCreationResult.Success(event)
                        // Clear selected file after successful creation
                        _selectedFile.value = null
                    },
                    onFailure = { exception ->
                        _eventCreationResult.value = EventCreationResult.Error(
                            exception.message ?: "Failed to create event"
                        )
                    }
                )
            } catch (e: Exception) {
                _eventCreationResult.value = EventCreationResult.Error(
                    e.message ?: "An unexpected error occurred"
                )
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Sets the selected file.
     *
     * @param uri The URI of the selected file.
     */
    fun setSelectedFile(uri: Uri?) {
        _selectedFile.value = uri
    }

    /**
     * Validates the input fields for creating a new event.
     *
     * @param title The title of the event.
     * @param body The body content of the event.
     * @param groupId The selected group ID.
     * @return A NewEventValidationResult indicating whether the inputs are valid.
     */
    fun validateInputs(
        title: String,
        body: String,
        groupId: Int?
    ): NewEventValidationResult {
        val isTitleValid = title.isNotBlank()
        val isBodyValid = body.isNotBlank()
        val isGroupValid = groupId != null

        return NewEventValidationResult(
            isTitleValid,
            isBodyValid,
            isGroupValid
        )
    }

    /**
     * Sealed class representing the result of event creation.
     */
    sealed class EventCreationResult {
        /**
         * Represents a successful event creation.
         *
         * @property event The created event.
         */
        data class Success(val event: Event) : EventCreationResult()

        /**
         * Represents a failed event creation.
         *
         * @property errorMessage The error message.
         */
        data class Error(val errorMessage: String) : EventCreationResult()
    }
}

/**
 * Data class representing the validation result for new event inputs.
 *
 * @property isTitleValid Whether the title is valid.
 * @property isBodyValid Whether the body is valid.
 * @property isGroupValid Whether the group selection is valid.
 */
data class NewEventValidationResult(
    val isTitleValid: Boolean,
    val isBodyValid: Boolean,
    val isGroupValid: Boolean
)
