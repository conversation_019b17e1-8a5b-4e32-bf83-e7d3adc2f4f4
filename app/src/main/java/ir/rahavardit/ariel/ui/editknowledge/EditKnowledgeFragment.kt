package ir.rahavardit.ariel.ui.editknowledge

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.model.Knowledge
import ir.rahavardit.ariel.databinding.FragmentEditKnowledgeBinding

/**
 * Fragment for editing an existing knowledge item.
 */
class EditKnowledgeFragment : Fragment() {

    private var _binding: FragmentEditKnowledgeBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: EditKnowledgeViewModel
    private lateinit var sessionManager: SessionManager
    private var shortUuid: String? = null
    private var knowledge: Knowledge? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(EditKnowledgeViewModel::class.java)
        _binding = FragmentEditKnowledgeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        shortUuid = arguments?.getString("shortUuid")
        knowledge = arguments?.getParcelable("knowledge")

        // Check if user is a superuser
        if (!sessionManager.getUserIsSuperuser()) {
            // Only superusers can edit knowledge items, navigate back
            Toast.makeText(
                requireContext(),
                "Only administrators can edit knowledge items",
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        // Check if we have the knowledge data
        if (knowledge == null) {
            Toast.makeText(
                requireContext(),
                "Knowledge data not found",
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        // Populate the form with the knowledge data
        populateForm()
        setupListeners()
        observeViewModel()
    }

    /**
     * Populates the form with the knowledge data.
     */
    private fun populateForm() {
        knowledge?.let {
            binding.etKnowledgeTitle.setText(it.title)
            binding.etKnowledgeBody.setText(it.body)
        }
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        binding.btnSubmit.setOnClickListener {
            val title = binding.etKnowledgeTitle.text.toString().trim()
            val body = binding.etKnowledgeBody.text.toString().trim()

            val validationResult = viewModel.validateInputs(title, body)

            when {
                !validationResult.isTitleValid -> {
                    binding.tilKnowledgeTitle.error = getString(R.string.please_enter_title)
                }
                !validationResult.isBodyValid -> {
                    binding.tilKnowledgeBody.error = getString(R.string.please_enter_body)
                }
                else -> {
                    // Clear any previous errors
                    binding.tilKnowledgeTitle.error = null
                    binding.tilKnowledgeBody.error = null

                    // Update knowledge
                    val token = sessionManager.getAuthToken()
                    if (token != null && shortUuid != null) {
                        viewModel.updateKnowledge(token, shortUuid!!, title, body)
                    } else if (token == null) {
                        showError(getString(R.string.authentication_token_not_found))
                    } else {
                        showError(getString(R.string.knowledge_id_not_found))
                    }
                }
            }
        }
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnSubmit.isEnabled = !isLoading
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                showError(errorMessage)
            } else {
                binding.tvError.visibility = View.GONE
            }
        }

        viewModel.knowledgeUpdateResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is EditKnowledgeViewModel.KnowledgeUpdateResult.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.knowledge_updated_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Navigate back to knowledge details
                    val bundle = Bundle().apply {
                        putString("shortUuid", result.knowledge.shortUuid)
                    }
                    findNavController().navigate(
                        R.id.action_editKnowledgeFragment_to_knowledgeDetailsFragment,
                        bundle
                    )
                }
                is EditKnowledgeViewModel.KnowledgeUpdateResult.Error -> {
                    showError(result.errorMessage)
                }
            }
        }
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to show.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
