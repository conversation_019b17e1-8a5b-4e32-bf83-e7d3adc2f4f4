package ir.rahavardit.ariel.ui.newticket

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.model.CategoryResponse
import ir.rahavardit.ariel.data.model.Priority
import ir.rahavardit.ariel.databinding.FragmentNewTicketBinding
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * Fragment for creating a new ticket.
 */
class NewTicketFragment : Fragment() {

    private var _binding: FragmentNewTicketBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: NewTicketViewModel
    private lateinit var sessionManager: SessionManager

    private var selectedCategory: String? = null
    private var selectedPriority: String? = null
    private var currentPhotoUri: Uri? = null

    // File picker launcher
    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                handleSelectedFile(uri)
            }
        }
    }

    // Camera launcher
    private val cameraLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success ->
        if (success && currentPhotoUri != null) {
            handleSelectedFile(currentPhotoUri!!)
        }
    }

    // Camera permission launcher
    private val cameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            openCamera()
        } else {
            Toast.makeText(requireContext(), "Camera permission is required to take photos", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(NewTicketViewModel::class.java)
        _binding = FragmentNewTicketBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())

        // Check if user is a superuser
        val user = sessionManager.getUser()
        if (user != null && user.isSuperuser) {
            // Superusers cannot create tickets, navigate back
            Toast.makeText(
                requireContext(),
                "Administrators cannot create tickets",
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        setupListeners()
        observeViewModel()
        fetchData()
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        // Set up file selection button
        binding.btnSelectFile.setOnClickListener {
            openFilePicker()
        }

        // Set up camera button
        binding.btnTakePhoto.setOnClickListener {
            checkCameraPermissionAndTakePhoto()
        }

        binding.btnSubmit.setOnClickListener {
            val title = binding.etTicketTitle.text.toString().trim()
            val message = binding.etMessage.text.toString().trim()

            val validationResult = viewModel.validateInputs(
                title,
                message,
                selectedCategory,
                selectedPriority
            )

            when {
                !validationResult.isTitleValid -> {
                    binding.tilTicketTitle.error = getString(R.string.please_enter_title)
                }
                !validationResult.isMessageValid -> {
                    binding.tilMessage.error = getString(R.string.please_enter_message)
                }
                !validationResult.isCategoryValid -> {
                    binding.tilCategory.error = getString(R.string.please_select_category)
                }
                !validationResult.isPriorityValid -> {
                    binding.tilPriority.error = getString(R.string.please_select_priority)
                }
                else -> {
                    // Clear any previous errors
                    binding.tilTicketTitle.error = null
                    binding.tilMessage.error = null
                    binding.tilCategory.error = null
                    binding.tilPriority.error = null

                    // Create ticket
                    val token = sessionManager.getAuthToken()
                    if (token != null) {
                        viewModel.createTicket(
                            token,
                            title,
                            message,
                            selectedCategory!!,
                            selectedPriority!!,
                            requireContext() // Pass context for file operations
                        )
                    } else {
                        showError("Authentication token not found")
                    }
                }
            }
        }

        // Set up category dropdown
        binding.dropdownCategory.setOnItemClickListener { _, _, position, _ ->
            val categories = viewModel.categories.value
            if (categories != null && position < categories.size) {
                selectedCategory = categories[position].persianName
                binding.tilCategory.error = null
            }
        }

        // Set up priority dropdown
        binding.dropdownPriority.setOnItemClickListener { _, _, position, _ ->
            val priorities = viewModel.priorities.value
            if (priorities != null && position < priorities.size) {
                selectedPriority = priorities[position].value
                binding.tilPriority.error = null
            }
        }
    }

    /**
     * Observes changes in the ViewModel.
     */
    private fun observeViewModel() {
        viewModel.categories.observe(viewLifecycleOwner) { categories ->
            setupCategoriesDropdown(categories)
        }

        viewModel.priorities.observe(viewLifecycleOwner) { priorities ->
            setupPrioritiesDropdown(priorities)
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnSubmit.isEnabled = !isLoading
            binding.btnSelectFile.isEnabled = !isLoading
            binding.btnTakePhoto.isEnabled = !isLoading

            // Maintain button highlighting during loading
            if (isLoading) {
                val selectedFile = viewModel.selectedFile.value
                if (selectedFile != null) {
                    val fileName = getFileName(selectedFile)
                    val isPhoto = fileName.startsWith("TICKET-") && fileName.endsWith(".jpg")
                    if (isPhoto) {
                        binding.btnTakePhoto.alpha = 0.8f
                        binding.btnSelectFile.alpha = 0.4f
                    } else {
                        binding.btnSelectFile.alpha = 0.8f
                        binding.btnTakePhoto.alpha = 0.4f
                    }
                }
            }
        }

        viewModel.selectedFile.observe(viewLifecycleOwner) { uri ->
            if (uri != null) {
                val fileName = getFileName(uri)
                val isPhoto = fileName.startsWith("TICKET-") && fileName.endsWith(".jpg")

                if (isPhoto) {
                    binding.tvSelectedFile.text = "📷 Photo to be sent: $fileName"
                    // Highlight camera button
                    binding.btnTakePhoto.alpha = 1.0f
                    binding.btnSelectFile.alpha = 0.6f
                } else {
                    binding.tvSelectedFile.text = "📄 File to be sent: $fileName"
                    // Highlight file button
                    binding.btnSelectFile.alpha = 1.0f
                    binding.btnTakePhoto.alpha = 0.6f
                }
                binding.tvSelectedFile.visibility = View.VISIBLE
            } else {
                binding.tvSelectedFile.visibility = View.GONE
                // Reset button states
                binding.btnSelectFile.alpha = 1.0f
                binding.btnTakePhoto.alpha = 1.0f
            }
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                showError(errorMessage)
            } else {
                binding.tvError.visibility = View.GONE
            }
        }

        viewModel.ticketCreationResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is NewTicketViewModel.TicketCreationResult.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.ticket_created_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Navigate to ticket details
                    val bundle = Bundle().apply {
                        putString("shortUuid", result.ticket.shortUuid)
                    }
                    findNavController().navigate(
                        R.id.action_nav_new_ticket_to_ticketDetailsFragment,
                        bundle
                    )
                }
                is NewTicketViewModel.TicketCreationResult.Error -> {
                    showError(result.errorMessage)
                }
            }
        }
    }

    /**
     * Fetches categories and priorities from the API.
     */
    private fun fetchData() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchCategories(token)
            viewModel.fetchPriorities(token)
        } else {
            showError("Authentication token not found")
        }
    }

    /**
     * Sets up the categories dropdown.
     *
     * @param categories The list of categories to display.
     */
    private fun setupCategoriesDropdown(categories: List<CategoryResponse>) {
        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            categories.map { it.persianName }
        )
        binding.dropdownCategory.setAdapter(adapter)
    }

    /**
     * Sets up the priorities dropdown.
     *
     * @param priorities The list of priorities to display.
     */
    private fun setupPrioritiesDropdown(priorities: List<Priority>) {
        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            priorities.map { it.label }
        )
        binding.dropdownPriority.setAdapter(adapter)
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to display.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }

    /**
     * Opens the file picker to select a file.
     */
    private fun openFilePicker() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "*/*" // Allow all file types
        }
        filePickerLauncher.launch(intent)
    }

    /**
     * Handles the selected file.
     *
     * @param uri The URI of the selected file.
     */
    private fun handleSelectedFile(uri: Uri) {
        try {
            // Check file size (max 5MB)
            val fileSize = getFileSize(uri)
            if (fileSize > 5 * 1024 * 1024) { // 5MB in bytes
                Toast.makeText(requireContext(), getString(R.string.file_too_large), Toast.LENGTH_SHORT).show()
                return
            }

            // Set the selected file in the ViewModel
            viewModel.setSelectedFile(uri)
        } catch (e: Exception) {
            Toast.makeText(requireContext(), getString(R.string.error_selecting_file), Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Gets the file name from a URI.
     *
     * @param uri The URI to get the file name from.
     * @return The file name.
     */
    private fun getFileName(uri: Uri): String {
        var fileName = "unknown_file"

        requireContext().contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val displayNameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                if (displayNameIndex != -1) {
                    fileName = cursor.getString(displayNameIndex)
                }
            }
        }

        return fileName
    }

    /**
     * Gets the file size from a URI.
     *
     * @param uri The URI to get the file size from.
     * @return The file size in bytes.
     */
    private fun getFileSize(uri: Uri): Long {
        var fileSize = 0L

        requireContext().contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE)
                if (sizeIndex != -1) {
                    fileSize = cursor.getLong(sizeIndex)
                }
            }
        }

        return fileSize
    }

    /**
     * Checks camera permission and takes a photo.
     */
    private fun checkCameraPermissionAndTakePhoto() {
        when {
            ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED -> {
                openCamera()
            }
            else -> {
                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }

    /**
     * Opens the camera to take a photo.
     */
    private fun openCamera() {
        try {
            val photoFile = createImageFile()
            currentPhotoUri = FileProvider.getUriForFile(
                requireContext(),
                "${requireContext().packageName}.fileprovider",
                photoFile
            )
            cameraLauncher.launch(currentPhotoUri)
        } catch (e: Exception) {
            Toast.makeText(requireContext(), "Error opening camera", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Creates a temporary image file for the camera.
     */
    private fun createImageFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMddHHmmss", Locale.US).format(Date())
        val imageFileName = "TICKET-${timeStamp}"
        val storageDir = requireContext().getExternalFilesDir(null)
        return File(storageDir, "${imageFileName}.jpg")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
