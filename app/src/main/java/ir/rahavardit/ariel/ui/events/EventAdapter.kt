package ir.rahavardit.ariel.ui.events

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.data.model.Event
import ir.rahavardit.ariel.databinding.ItemEventBinding

/**
 * Adapter for displaying event items in a RecyclerView.
 *
 * @property onEventClick Callback for when an event item is clicked.
 */
class EventAdapter(private val onEventClick: (Event) -> Unit) :
    ListAdapter<Event, EventAdapter.EventViewHolder>(EventDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EventViewHolder {
        val binding = ItemEventBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return EventViewHolder(binding)
    }

    override fun onBindViewHolder(holder: EventViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * ViewHolder for an event item.
     */
    inner class EventViewHolder(private val binding: ItemEventBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onEventClick(getItem(position))
                }
            }
        }

        /**
         * Binds an event item to the ViewHolder.
         *
         * @param event The event item to bind.
         */
        fun bind(event: Event) {
            binding.apply {
                tvEventTitle.text = event.title
                tvEventGroup.text = event.group.name
                tvEventId.text = event.shortUuid
                tvCreatedDate.text = event.createdJalali
            }
        }
    }

    /**
     * DiffUtil callback for comparing event items.
     */
    private class EventDiffCallback : DiffUtil.ItemCallback<Event>() {
        override fun areItemsTheSame(oldItem: Event, newItem: Event): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Event, newItem: Event): Boolean {
            return oldItem == newItem
        }
    }
}
