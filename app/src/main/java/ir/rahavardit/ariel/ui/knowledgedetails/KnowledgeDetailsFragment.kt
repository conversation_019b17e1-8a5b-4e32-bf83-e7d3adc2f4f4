package ir.rahavardit.ariel.ui.knowledgedetails

import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import android.widget.Toast

import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.model.Knowledge
import ir.rahavardit.ariel.databinding.FragmentKnowledgeDetailsBinding
import ir.rahavardit.ariel.utils.HtmlRenderer

/**
 * Fragment for displaying the details of a knowledge item.
 */
class KnowledgeDetailsFragment : Fragment() {

    private var _binding: FragmentKnowledgeDetailsBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: KnowledgeDetailsViewModel
    private lateinit var sessionManager: SessionManager
    private var shortUuid: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(KnowledgeDetailsViewModel::class.java)
        _binding = FragmentKnowledgeDetailsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        shortUuid = arguments?.getString("shortUuid")

        observeViewModel()
        fetchKnowledgeDetails()
        setupDeleteButton()
        setupEditButton()
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.knowledgeState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is KnowledgeDetailsViewModel.KnowledgeState.Loading -> {
                    showLoading()
                }
                is KnowledgeDetailsViewModel.KnowledgeState.Success -> {
                    hideLoading()
                    displayKnowledge(state.knowledge)
                }
                is KnowledgeDetailsViewModel.KnowledgeState.Error -> {
                    hideLoading()
                    showError(state.errorMessage)
                }
            }
        }

        viewModel.deleteState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is KnowledgeDetailsViewModel.DeleteState.Loading -> {
                    showLoading()
                }
                is KnowledgeDetailsViewModel.DeleteState.Success -> {
                    hideLoading()
                    Toast.makeText(requireContext(), getString(R.string.knowledge_deleted_successfully), Toast.LENGTH_SHORT).show()
                    // Navigate to home after deletion
                    findNavController().navigate(R.id.nav_home)
                }
                is KnowledgeDetailsViewModel.DeleteState.Error -> {
                    hideLoading()
                    Toast.makeText(requireContext(), state.errorMessage, Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    /**
     * Displays the knowledge details in the UI.
     *
     * @param knowledge The knowledge item to display.
     */
    private fun displayKnowledge(knowledge: Knowledge) {
        binding.apply {
            cardKnowledge.visibility = View.VISIBLE
            tvKnowledgeTitle.text = knowledge.title
            tvKnowledgeDate.text = knowledge.createdJalali
            tvKnowledgeId.text = getString(R.string.id_format, knowledge.shortUuid)
            HtmlRenderer.applyToTextView(tvKnowledgeBody, knowledge.body)

            // Show edit and delete buttons only for superusers
            if (sessionManager.getUserIsSuperuser()) {
                btnEditKnowledge.visibility = View.VISIBLE
                btnDeleteKnowledge.visibility = View.VISIBLE
            } else {
                btnEditKnowledge.visibility = View.GONE
                btnDeleteKnowledge.visibility = View.GONE
            }
        }
    }

    /**
     * Shows the loading indicator and hides other views.
     */
    private fun showLoading() {
        binding.progressBar.visibility = View.VISIBLE
        binding.cardKnowledge.visibility = View.GONE
        binding.tvError.visibility = View.GONE
    }

    /**
     * Hides the loading indicator.
     */
    private fun hideLoading() {
        binding.progressBar.visibility = View.GONE
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to display.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
        binding.cardKnowledge.visibility = View.GONE
    }

    /**
     * Fetches knowledge details from the API.
     */
    private fun fetchKnowledgeDetails() {
        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            viewModel.fetchKnowledgeDetails(token, shortUuid!!)
        } else if (token == null) {
            showError(getString(R.string.authentication_token_not_found))
        } else {
            showError(getString(R.string.knowledge_id_not_found))
        }
    }

    /**
     * Sets up the delete button click listener.
     */
    private fun setupDeleteButton() {
        binding.btnDeleteKnowledge.setOnClickListener {
            showDeleteConfirmationDialog()
        }
    }

    /**
     * Shows a confirmation dialog before deleting the knowledge item.
     */
    private fun showDeleteConfirmationDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_delete_confirm, null)
        val titleTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_title)
        val messageTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_message)
        val confirmButton = dialogView.findViewById<Button>(R.id.btn_confirm)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)

        // Set the title and message
        titleTextView.text = getString(R.string.delete_knowledge_confirmation_title)
        messageTextView.text = getString(R.string.delete_knowledge_confirmation_message)

        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        confirmButton.setOnClickListener {
            deleteKnowledge()
            dialog.dismiss()
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * Deletes the knowledge item.
     */
    private fun deleteKnowledge() {
        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            viewModel.deleteKnowledge(token, shortUuid!!)
        } else if (token == null) {
            Toast.makeText(requireContext(), getString(R.string.authentication_token_not_found), Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(requireContext(), getString(R.string.knowledge_id_not_found), Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Sets up the edit button click listener.
     */
    private fun setupEditButton() {
        binding.btnEditKnowledge.setOnClickListener {
            navigateToEditKnowledge()
        }
    }

    /**
     * Navigates to the edit knowledge screen.
     */
    private fun navigateToEditKnowledge() {
        val knowledge = viewModel.currentKnowledge.value
        if (knowledge != null && shortUuid != null) {
            val bundle = Bundle().apply {
                putString("shortUuid", shortUuid)
                putParcelable("knowledge", knowledge)
            }
            findNavController().navigate(
                R.id.action_knowledgeDetailsFragment_to_editKnowledgeFragment,
                bundle
            )
        } else {
            Toast.makeText(requireContext(), getString(R.string.knowledge_data_not_found), Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
