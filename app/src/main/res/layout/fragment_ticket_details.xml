<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="6dp"
    tools:context=".ui.ticketdetails.TicketDetailsFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false" >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Status Alert Message (moved to very top) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_status_alert"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="4dp"
                android:layout_marginBottom="8dp"
                app:cardBackgroundColor="#FFF3E0"
                app:cardCornerRadius="4dp"
                app:cardElevation="0dp"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="8dp">

                    <TextView
                        android:id="@+id/tv_status_alert_message"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textColor="#FFF3E0"
                        android:textAppearance="?attr/textAppearanceCaption"
                        tools:text="Ticket Status Message" />

                    <ImageButton
                        android:id="@+id/btn_close_alert"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center_vertical"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="Close alert"
                        android:src="@android:drawable/ic_menu_close_clear_cancel" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Ticket Management Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_ticket_management"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="4dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp"
                app:strokeColor="@color/card_border"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <!-- author (right) -->
                        <TextView
                            android:id="@+id/tv_ticket_author"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:textAppearance="?attr/textAppearanceCaption"
                            tools:text="rozhan" />

                        <!-- full name (center) -->
                        <TextView
                            android:id="@+id/tv_author_full_name"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:textAppearance="?attr/textAppearanceCaption"
                            tools:text="سارا سروشی دانا" />

                        <!-- rating (left) -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end">

                            <RatingBar
                                android:id="@+id/rating_bar"
                                style="?android:attr/ratingBarStyleSmall"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layoutDirection="ltr"
                                android:isIndicator="true"
                                android:numStars="5"
                                android:stepSize="1"
                                tools:rating="5" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- groups -->
                    <TextView
                        android:id="@+id/tv_author_groups"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:text="Lab, School"
                        android:textAppearance="?attr/textAppearanceCaption"
                        android:layout_marginTop="8dp" />

                    <!-- Category and Priority in one row -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_ticket_category"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="@string/floating_label__category"
                            app:hintTextAppearance="@style/TextAppearance.MaterialComponents.Caption">

                            <AutoCompleteTextView
                                android:id="@+id/dropdown_ticket_category"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:textSize="12sp"
                                android:paddingTop="8dp"
                                android:paddingBottom="8dp"
                                tools:text="نرم‌افزار" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_ticket_priority"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:hint="@string/floating_label__priority"
                            app:hintTextAppearance="@style/TextAppearance.MaterialComponents.Caption">

                            <AutoCompleteTextView
                                android:id="@+id/dropdown_ticket_priority"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:textSize="12sp"
                                android:paddingTop="8dp"
                                android:paddingBottom="8dp"
                                tools:text="خیلی زیاد" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <!-- Status dropdown -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_ticket_status"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:hint="@string/floating_label__status"
                        app:hintTextAppearance="@style/TextAppearance.MaterialComponents.Caption">

                        <AutoCompleteTextView
                            android:id="@+id/dropdown_ticket_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:textSize="12sp"
                            android:paddingTop="8dp"
                            android:paddingBottom="8dp"
                            tools:text="در انتظار پاسخ" />
                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Parent Ticket Section (styled as author bubble) -->
            <LinearLayout
                android:id="@+id/layout_parent_ticket"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="4dp"
                android:orientation="horizontal">

                <!-- Right-aligned message bubble for parent ticket -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_parent_ticket"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="48dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp"
                    app:cardBackgroundColor="@color/chat_message_user_bg">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <!-- right -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="horizontal">

                                <!-- date -->
                                <TextView
                                    android:id="@+id/tv_ticket_date"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textAppearance="?attr/textAppearanceCaption"
                                    android:textColor="?android:attr/textColorSecondary"
                                    android:textSize="10sp"
                                    tools:text="یک‌شنبه ۰۰:۱۸:۲۹ ۱۴۰۳/۰۳/۲۷" />
                            </LinearLayout>

                            <TextView
                                android:id="@+id/tv_ticket_id"
                                android:gravity="end"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textAppearance="?attr/textAppearanceCaption"
                                android:textColor="?android:attr/textColorSecondary"
                                android:textSize="10sp"
                                tools:text="4588e326" />
                        </LinearLayout>

                        <!-- Title -->
                        <TextView
                            android:id="@+id/tv_ticket_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:textAppearance="?attr/textAppearanceSubtitle1"
                            android:textColor="?attr/colorOnSurface"
                            android:textStyle="bold"
                            tools:text="موهمند ناستیک آران‌ای دست‌مزدها انه‌آ" />

                        <!-- Message content -->
                        <TextView
                            android:id="@+id/tv_ticket_message"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:autoLink="web"
                            android:linksClickable="true"
                            tools:text="نشور خانبالیق باکیی دمپای --  گاریبالدینی فونکن کیست‌های حوالتست دادن‌هایش راندهای مرتبط‌تر هریره ابراهیم‌بیگلو! زبانتان وجهانگیریه مجردانیم منشاشان گروسان كنوانسیونی ایکا علاقه‌ها می‌نشست بدوائی می‌گیرندو --  چنبرش طرازنده‌ی: کروم؟" />

                        <!-- File Attachment Section -->
                        <LinearLayout
                            android:id="@+id/layout_ticket_file"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:orientation="vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/tv_ticket_file_name"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textAppearance="?attr/textAppearanceBody2"
                                tools:text="document.pdf" />

                            <Button
                                android:id="@+id/btn_ticket_download_file"
                                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/download_file"
                                app:icon="@android:drawable/ic_menu_save" />
                        </LinearLayout>

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Children Tickets Section -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_responses"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:padding="4dp"
                tools:itemCount="3"
                tools:listitem="@layout/item_ticket_response" />

            <!-- Reply Form Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:orientation="vertical">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_reply_form"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="4dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="0dp"
                    app:strokeColor="@color/card_border"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/reply_to_ticket"
                            android:textAppearance="?attr/textAppearanceCaption"
                            android:textStyle="bold" /> -->

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_reply_message"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:hint="@string/ticket_message">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_reply_message"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="top|start"
                                android:inputType="textMultiLine"
                                android:lines="5"
                                android:minLines="5" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <LinearLayout
                            android:id="@+id/layout_reply_file"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <Button
                                    android:id="@+id/btn_select_reply_file"
                                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:layout_marginEnd="8dp"
                                    android:text="@string/select_file"
                                    android:textColor="@color/select_file_button_fg"
                                    android:backgroundTint="@color/select_file_button_bg"
                                    app:strokeWidth="0dp"
                                    app:icon="@android:drawable/ic_menu_upload" />

                                <Button
                                    android:id="@+id/btn_take_reply_photo"
                                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:layout_marginStart="8dp"
                                    android:text="@string/take_photo"
                                    android:textColor="@color/take_photo_button_fg"
                                    android:backgroundTint="@color/take_photo_button_bg"
                                    app:strokeWidth="0dp"
                                    app:icon="@android:drawable/ic_menu_camera" />

                            </LinearLayout>

                            <TextView
                                android:id="@+id/tv_selected_reply_file"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:ellipsize="middle"
                                android:singleLine="true"
                                android:visibility="gone"
                                tools:text="selected_file.pdf"
                                tools:visibility="visible" />
                        </LinearLayout>

                        <Button
                            android:id="@+id/btn_submit_reply"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="@string/submit_reply"
                            android:textColor="@color/submit_button_fg"
                            android:backgroundTint="@color/submit_button_bg"
                            app:strokeWidth="0dp" />

                        <ProgressBar
                            android:id="@+id/progress_bar_reply"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="8dp"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tv_reply_error"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:gravity="center"
                            android:textColor="@android:color/holo_red_dark"
                            android:visibility="gone"
                            tools:text="Error message"
                            tools:visibility="visible" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>

            <!-- Changes Section -->
            <TextView
                android:id="@+id/tv_changes_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="4dp"
                android:layout_marginTop="8dp"
                android:text="@string/ticket_changes"
                android:textAppearance="?attr/textAppearanceCaption"
                android:textStyle="bold"
                android:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_changes"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:visibility="gone"
                tools:itemCount="2"
                tools:listitem="@layout/item_ticket_change" />

            <!-- Delete Button (Only visible for superusers) - Moved to bottom -->
            <Button
                android:id="@+id/btn_delete_ticket"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="4dp"
                android:layout_marginTop="8dp"
                android:text="@string/delete"
                android:textColor="@color/delete_button_fg"
                android:backgroundTint="@color/delete_button_bg"
                app:strokeWidth="0dp"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="4dp"
        android:gravity="center"
        android:textAppearance="?attr/textAppearanceBody1"
        android:textColor="?attr/colorError"
        android:visibility="gone"
        tools:text="Error loading ticket details"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
