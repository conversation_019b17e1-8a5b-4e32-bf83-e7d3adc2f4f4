<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="120dp"
    android:layout_margin="6dp"
    app:cardElevation="0dp"
    app:cardCornerRadius="8dp"
    app:cardBackgroundColor="?attr/colorSurface"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">
    <!-- app:strokeColor="@color/card_border" -->
    <!-- app:strokeWidth="1dp" -->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="4dp">

        <ImageView
            android:id="@+id/iv_box_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginBottom="4dp"
            android:visibility="gone"
            tools:src="@drawable/ic_menu_tickets"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_box_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/statistics_text_color"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="همه تیکت‌ها" />

        <TextView
            android:id="@+id/tv_box_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center"
            android:visibility="gone"
            tools:text="۱۳"
            tools:visibility="visible" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
