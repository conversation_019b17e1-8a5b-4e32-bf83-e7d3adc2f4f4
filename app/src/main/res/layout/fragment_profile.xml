<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.profile.ProfileFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="256dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:contentScrim="?attr/colorPrimary"
            app:expandedTitleMarginEnd="64dp"
            app:expandedTitleMarginStart="48dp"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/side_nav_bar"
                app:layout_collapseMode="parallax">

                <ImageView
                    android:id="@+id/profile_image"
                    android:layout_width="96dp"
                    android:layout_height="96dp"
                    android:contentDescription="@string/profile__image"
                    android:src="@drawable/ic_person"
                    android:background="@color/white"
                    android:padding="16dp"
                    android:elevation="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.4" />

                <TextView
                    android:id="@+id/profile_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textColor="@color/white"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/profile_image"
                    tools:text="John Doe" />

                <TextView
                    android:id="@+id/profile_position"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    android:textColor="@color/white"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/profile_name"
                    tools:text="Developer at ACME Inc" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="6dp">

            <Button
                android:id="@+id/btn_change_password"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/profile__change_password"
                android:layout_margin="4dp"
                android:textColor="@color/change_password_button_fg"
                android:backgroundTint="@color/change_password_button_bg"
                app:strokeWidth="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_margin="4dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/btn_change_password"
                app:strokeColor="@color/card_border"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/profile__information"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"
                        android:background="@color/gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__username"
                            android:textStyle="bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/profile_username"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="user1" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__email"
                            android:textStyle="bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/profile_email"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="<EMAIL>" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__company"
                            android:textStyle="bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/profile_company"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="ACME Inc" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__position"
                            android:textStyle="bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/profile_position_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="Developer" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__first_name"
                            android:textStyle="bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/profile_first_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="John" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__last_name"
                            android:textStyle="bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/profile_last_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="Doe" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__description"
                            android:textStyle="bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/profile_description"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="Software developer with 5 years of experience" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__gender"
                            android:textStyle="bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/profile_gender"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="Male" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__is_superuser"
                            android:textStyle="bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/profile_is_superuser"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="No" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__is_limited_admin"
                            android:textStyle="bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/profile_is_limited_admin"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="Yes" />
                    </LinearLayout>

                    <!-- id -->
                    <!-- <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__id"
                            android:textStyle="bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/profile_user_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="6" />
                    </LinearLayout> -->

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__short_uuid"
                            android:textStyle="bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/profile_short_uuid"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="da1e120e" />
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/error_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                android:textColor="@android:color/holo_red_dark"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Error loading profile" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
