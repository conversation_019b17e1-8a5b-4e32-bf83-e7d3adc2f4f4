<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="0dp"
    app:strokeColor="@color/card_border"
    app:strokeWidth="1dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_ticket_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="?attr/textAppearanceSubtitle2"
            android:textColor="?attr/colorOnSurface"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Ticket Title" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_priority"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="11sp"
            app:chipMinHeight="25dp"
            app:chipCornerRadius="4dp"
            app:chipStartPadding="8dp"
            app:chipEndPadding="8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_ticket_title"
            tools:text="خیلی زیاد" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textSize="11sp"
            app:chipMinHeight="25dp"
            app:chipCornerRadius="4dp"
            app:chipStartPadding="8dp"
            app:chipEndPadding="8dp"
            app:layout_constraintStart_toEndOf="@id/chip_priority"
            app:layout_constraintTop_toTopOf="@id/chip_priority"
            tools:text="در انتظار پاسخ" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textSize="11sp"
            app:chipMinHeight="25dp"
            app:chipCornerRadius="4dp"
            app:chipStartPadding="8dp"
            app:chipEndPadding="8dp"
            app:layout_constraintStart_toEndOf="@id/chip_status"
            app:layout_constraintTop_toTopOf="@id/chip_priority"
            tools:text="Software" />

        <TextView
            android:id="@+id/tv_ticket_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/chip_priority"
            tools:text="4588e326" />

        <TextView
            android:id="@+id/tv_separator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="8dp"
            android:text="•"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintEnd_toStartOf="@id/tv_ticket_id"
            app:layout_constraintTop_toBottomOf="@id/chip_priority" />

        <TextView
            android:id="@+id/tv_ticket_author"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginStart="4dp"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintEnd_toStartOf="@+id/tv_separator"
            app:layout_constraintTop_toBottomOf="@id/chip_priority"
            tools:text="rozhan" />

        <TextView
            android:id="@+id/tv_created_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/chip_priority"
            tools:text="یک‌شنبه ۰۰:۱۸:۲۹ ۱۴۰۳/۰۳/۲۷" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
