<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="0dp"
    app:strokeColor="@color/card_border"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- Action text at the top -->
        <TextView
            android:id="@+id/tv_change_action"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceSubtitle2"
            android:textColor="?attr/colorOnSurface"
            android:textStyle="bold"
            tools:text="دسته‌بندی به سرور تغییر یافت" />

        <!-- Bottom row with username on left and date on right -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_change_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="?attr/textAppearanceCaption"
                android:textColor="?android:attr/textColorSecondary"
                tools:text="یک‌شنبه ۰۸:۵۲:۲۷ ۱۴۰۲/۱۱/۰۸" />

            <TextView
                android:id="@+id/tv_change_username"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textAppearance="?attr/textAppearanceCaption"
                android:textColor="?android:attr/textColorSecondary"
                tools:text="jackson" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
